'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  CheckmarkCircle01Icon as CheckCircle2Icon,
  Cancel01Icon as XCircleIcon,
  Award01Icon as TrophyIcon,
  Rotate01Icon as RotateCcwIcon,
  ArrowLeft01Icon as ArrowLeftIcon,
  Home01Icon as HomeIcon
} from 'hugeicons-react';
import { Course, Quiz } from '@/types/lms';
import { useEnrollment } from '@/contexts/enrollment-context';

const ExamResultsPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const courseId = params.courseId as string;
  const examType = searchParams.get('type') || 'final';
  const examId = searchParams.get('examId');
  const score = parseInt(searchParams.get('score') || '0');
  const correctAnswers = parseInt(searchParams.get('correct') || '0');
  const totalQuestions = parseInt(searchParams.get('total') || '0');

  const { courseData, updateCourseProgress } = useEnrollment();
  const [answers, setAnswers] = useState<{ [key: string]: any }>({});
  const [results, setResults] = useState<{ [key: string]: boolean }>({});
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());

  // Get the current exam/quiz
  const getCurrentExam = (): Quiz | null => {
    if (examType === 'final') {
      return courseData.finalExam;
    }
    
    for (const courseModule of courseData.modules) {
      if (courseModule.moduleQuiz.id === examId) {
        return courseModule.moduleQuiz;
      }
      for (const chapter of courseModule.chapters) {
        if (chapter.quiz.id === examId) {
          return chapter.quiz;
        }
      }
    }
    return null;
  };

  const currentExam = getCurrentExam();

  // Get stored data from localStorage/sessionStorage
  useEffect(() => {
    const storedAnswers = sessionStorage.getItem(`exam_answers_${examId}`);
    const storedResults = sessionStorage.getItem(`exam_results_${examId}`);
    const storedFlags = sessionStorage.getItem(`exam_flags_${examId}`);

    if (storedAnswers) {
      setAnswers(JSON.parse(storedAnswers));
    }
    if (storedResults) {
      setResults(JSON.parse(storedResults));
    }
    if (storedFlags) {
      setFlaggedQuestions(new Set(JSON.parse(storedFlags)));
    }
  }, [examId]);

  const handleRetakeExam = () => {
    // Clear stored data
    sessionStorage.removeItem(`exam_answers_${examId}`);
    sessionStorage.removeItem(`exam_results_${examId}`);
    sessionStorage.removeItem(`exam_flags_${examId}`);
    
    // Go back to exam
    router.push(`/my-courses/${courseId}/exam?type=${examType}&examId=${examId}`);
  };

  const handleBackToCourse = () => {
    router.push(`/my-courses/${courseId}`);
  };

  const handleBackHome = () => {
    router.push('/my-courses');
  };

  if (!currentExam) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Hasil Ujian Tidak Ditemukan</h3>
            <p className="text-gray-600 mb-4">Hasil ujian yang diminta tidak dapat ditemukan.</p>
            <Button onClick={handleBackToCourse}>
              <ArrowLeftIcon className="mr-2 h-4 w-4" />
              Kembali ke Kursus
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isPassed = score >= currentExam.minimumScore;
  const incorrectAnswers = totalQuestions - correctAnswers;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b shadow-sm sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <TrophyIcon className="h-6 w-6 text-red-600" />
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Hasil {currentExam.title}</h1>
                <p className="text-sm text-gray-600">{courseData.name}</p>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Button variant="outline" onClick={handleBackToCourse}>
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                Kembali ke Kursus
              </Button>
              <Button variant="outline" onClick={handleBackHome}>
                <HomeIcon className="mr-2 h-4 w-4" />
                Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Results Summary */}
        <div className="max-w-4xl mx-auto mb-8">
          <Card className={`border-2 ${isPassed ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {isPassed ? (
                  <CheckCircle2Icon className="h-16 w-16 text-green-600" />
                ) : (
                  <XCircleIcon className="h-16 w-16 text-red-600" />
                )}
              </div>
              <CardTitle className="text-2xl">
                {isPassed ? 'Selamat! Anda Lulus' : 'Maaf, Anda Belum Lulus'}
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <div className="text-4xl font-bold text-gray-900">
                {score}%
              </div>
              <div className="text-gray-600">
                {correctAnswers} dari {totalQuestions} soal dijawab benar
              </div>
              <div className="text-sm text-gray-500">
                Nilai minimum untuk lulus: {currentExam.minimumScore}%
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div className="bg-white rounded-lg p-4 border">
                  <div className="text-2xl font-bold text-blue-600">{totalQuestions}</div>
                  <div className="text-sm text-gray-500">Total Soal</div>
                </div>
                <div className="bg-white rounded-lg p-4 border">
                  <div className="text-2xl font-bold text-green-600">{correctAnswers}</div>
                  <div className="text-sm text-gray-500">Benar</div>
                </div>
                <div className="bg-white rounded-lg p-4 border">
                  <div className="text-2xl font-bold text-red-600">{incorrectAnswers}</div>
                  <div className="text-sm text-gray-500">Salah</div>
                </div>
                <div className="bg-white rounded-lg p-4 border">
                  <div className="text-2xl font-bold text-purple-600">{Math.round((correctAnswers / totalQuestions) * 100)}%</div>
                  <div className="text-sm text-gray-500">Akurasi</div>
                </div>
              </div>
              
              <div className="flex flex-wrap justify-center gap-3 mt-6">
                {!isPassed && currentExam.attempts < currentExam.maxAttempts && (
                  <Button onClick={handleRetakeExam}>
                    <RotateCcwIcon className="mr-2 h-4 w-4" />
                    Retake Ujian
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>


        {/* Bottom Navigation */}
        <div className="max-w-4xl mx-auto mt-8 text-center">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Sudah selesai mereview?</h3>
              <div className="flex flex-wrap justify-center gap-3">
                {!isPassed && currentExam.attempts < currentExam.maxAttempts && (
                  <Button onClick={handleRetakeExam}>
                    <RotateCcwIcon className="mr-2 h-4 w-4" />
                    Retake Ujian
                  </Button>
                )}
                
                <Button variant="iai" onClick={handleBackToCourse}>
                  <ArrowLeftIcon className="mr-2 h-4 w-4" />
                  Kembali ke Kursus
                </Button>

                <Button variant="outline" onClick={handleBackHome}>
                  <HomeIcon className="mr-2 h-4 w-4" />
                  Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ExamResultsPage;