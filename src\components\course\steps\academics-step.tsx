import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Book, Hourglass, Award, X } from 'lucide-react';
import { CourseData, AcademicsData } from '../course-creation-wizard';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AcademicsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function AcademicsStep({ data, onUpdate }: AcademicsStepProps) {
  const academics = data.academics || { credits: 0, workload: '', assessment: [] };
  const [newAssessment, setNewAssessment] = useState('');

  const handleUpdate = (field: keyof AcademicsData, value: string | number | string[]) => {
    onUpdate({
      academics: {
        ...academics,
        [field]: value,
      },
    });
  };

  const addAssessment = () => {
    if (newAssessment.trim() !== '' && !academics.assessment.includes(newAssessment.trim())) {
      handleUpdate('assessment', [...academics.assessment, newAssessment.trim()]);
      setNewAssessment('');
    }
  };

  const removeAssessment = (index: number) => {
    const updatedAssessment = academics.assessment.filter((_, i) => i !== index);
    handleUpdate('assessment', updatedAssessment);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Informasi Akademik</CardTitle>
        <CardDescription>Detail terkait struktur akademik dan penilaian kursus.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2">
          <Book className="h-5 w-5 text-gray-500" />
          <Label htmlFor="credits">Kredit</Label>
        </div>
        <Input
          id="credits"
          type="number"
          value={academics.credits}
          onChange={(e) => handleUpdate('credits', parseInt(e.target.value))}
          placeholder="Contoh: 12"
        />

        <div className="flex items-center space-x-2">
          <Hourglass className="h-5 w-5 text-gray-500" />
          <Label htmlFor="workload">Beban Kerja</Label>
        </div>
        <Input
          id="workload"
          type="text"
          value={academics.workload}
          onChange={(e) => handleUpdate('workload', e.target.value)}
          placeholder="Contoh: 12-15 jam/minggu"
        />

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Award className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newAssessment">Penilaian</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newAssessment"
              value={newAssessment}
              onChange={(e) => setNewAssessment(e.target.value)}
              placeholder="Tambahkan metode penilaian baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addAssessment();
                }
              }}
            />
            <Button type="button" onClick={addAssessment}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {academics.assessment.map((item, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {item}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removeAssessment(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}