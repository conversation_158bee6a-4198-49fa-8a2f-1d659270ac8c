import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Briefcase, Building, DollarSign, X } from 'lucide-react';
import { CourseData, CareersData } from '../course-creation-wizard';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface CareersStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CareersStep({ data, onUpdate }: CareersStepProps) {
  const careers = data.careers || { outcomes: [], industries: [], averageSalary: '' };
  const [newOutcome, setNewOutcome] = useState('');
  const [newIndustry, setNewIndustry] = useState('');

  const handleUpdate = (field: keyof CareersData, value: string | string[]) => {
    onUpdate({
      careers: {
        ...careers,
        [field]: value,
      },
    });
  };

  const addOutcome = () => {
    if (newOutcome.trim() !== '' && !careers.outcomes.includes(newOutcome.trim())) {
      handleUpdate('outcomes', [...careers.outcomes, newOutcome.trim()]);
      setNewOutcome('');
    }
  };

  const removeOutcome = (index: number) => {
    const updatedOutcomes = careers.outcomes.filter((_, i) => i !== index);
    handleUpdate('outcomes', updatedOutcomes);
  };

  const addIndustry = () => {
    if (newIndustry.trim() !== '' && !careers.industries.includes(newIndustry.trim())) {
      handleUpdate('industries', [...careers.industries, newIndustry.trim()]);
      setNewIndustry('');
    }
  };

  const removeIndustry = (index: number) => {
    const updatedIndustries = careers.industries.filter((_, i) => i !== index);
    handleUpdate('industries', updatedIndustries);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Peluang Karir</CardTitle>
        <CardDescription>Detail terkait hasil karir dan industri yang relevan setelah kursus.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Briefcase className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newOutcome">Hasil</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newOutcome"
              value={newOutcome}
              onChange={(e) => setNewOutcome(e.target.value)}
              placeholder="Tambahkan hasil karir baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addOutcome();
                }
              }}
            />
            <Button type="button" onClick={addOutcome}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {careers.outcomes.map((outcome, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {outcome}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removeOutcome(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Building className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newIndustry">Industri</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newIndustry"
              value={newIndustry}
              onChange={(e) => setNewIndustry(e.target.value)}
              placeholder="Tambahkan industri baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addIndustry();
                }
              }}
            />
            <Button type="button" onClick={addIndustry}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {careers.industries.map((industry, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {industry}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removeIndustry(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-gray-500" />
          <Label htmlFor="averageSalary">Rata-rata Gaji</Label>
        </div>
        <Input
          id="averageSalary"
          type="text"
          value={careers.averageSalary}
          onChange={(e) => handleUpdate('averageSalary', e.target.value)}
          placeholder="Contoh: Rp780.000.000 - Rp1.140.000.000 per tahun"
        />
      </CardContent>
    </Card>
  );
}