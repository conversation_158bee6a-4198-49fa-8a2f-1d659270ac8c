import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, ClipboardList, BookOpen, X } from 'lucide-react';
import { CourseData, AdmissionsData } from '../course-creation-wizard';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AdmissionsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function AdmissionsStep({ data, onUpdate }: AdmissionsStepProps) {
  const admissions = data.admissions || { requirements: [], applicationDeadline: '', prerequisites: [] };
  const [newRequirement, setNewRequirement] = useState('');
  const [newPrerequisite, setNewPrerequisite] = useState('');

  const handleUpdate = (field: keyof AdmissionsData, value: string | string[]) => {
    onUpdate({
      admissions: {
        ...admissions,
        [field]: value,
      },
    });
  };

  const addRequirement = () => {
    if (newRequirement.trim() !== '' && !admissions.requirements.includes(newRequirement.trim())) {
      handleUpdate('requirements', [...admissions.requirements, newRequirement.trim()]);
      setNewRequirement('');
    }
  };

  const removeRequirement = (index: number) => {
    const updatedRequirements = admissions.requirements.filter((_, i) => i !== index);
    handleUpdate('requirements', updatedRequirements);
  };

  const addPrerequisite = () => {
    if (newPrerequisite.trim() !== '' && !admissions.prerequisites.includes(newPrerequisite.trim())) {
      handleUpdate('prerequisites', [...admissions.prerequisites, newPrerequisite.trim()]);
      setNewPrerequisite('');
    }
  };

  const removePrerequisite = (index: number) => {
    const updatedPrerequisites = admissions.prerequisites.filter((_, i) => i !== index);
    handleUpdate('prerequisites', updatedPrerequisites);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Informasi Pendaftaran</CardTitle>
        <CardDescription>Detail terkait persyaratan pendaftaran dan prasyarat kursus.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <ClipboardList className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newRequirement">Persyaratan</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newRequirement"
              value={newRequirement}
              onChange={(e) => setNewRequirement(e.target.value)}
              placeholder="Tambahkan persyaratan baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addRequirement();
                }
              }}
            />
            <Button type="button" onClick={addRequirement}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {admissions.requirements.map((req, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {req}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removeRequirement(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-gray-500" />
          <Label htmlFor="applicationDeadline">Batas Waktu Pendaftaran</Label>
        </div>
        <Input
          id="applicationDeadline"
          type="text" // Could be a date picker in a real app
          value={admissions.applicationDeadline}
          onChange={(e) => handleUpdate('applicationDeadline', e.target.value)}
          placeholder="Contoh: 2024-12-31"
        />

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <BookOpen className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newPrerequisite">Prasyarat</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newPrerequisite"
              value={newPrerequisite}
              onChange={(e) => setNewPrerequisite(e.target.value)}
              placeholder="Tambahkan prasyarat baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addPrerequisite();
                }
              }}
            />
            <Button type="button" onClick={addPrerequisite}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {admissions.prerequisites.map((req, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {req}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removePrerequisite(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}