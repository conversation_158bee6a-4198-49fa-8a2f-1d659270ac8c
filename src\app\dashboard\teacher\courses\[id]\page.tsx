'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';
import { CourseCreationWizard, CourseData } from '@/components/course/course-creation-wizard';
import { transformApiCourseToWizardData, transformWizardDataToApiUpdate, ApiCourse, ApiModule, ApiChapter, ApiQuiz, ApiQuestion } from '@/utils/course-data-transformer';

export default function CourseEditPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const router = useRouter();
  
  const [courseData, setCourseData] = useState<CourseData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (id) {
      fetchCourseData();
    }
  }, [id]);

  const fetchCourseData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const user = authStorage.getUser();
      if (!user) {
        setError('Please log in to edit course');
        return;
      }

      // Fetch course data
      const response = await fetch(`/api/courses/${id}?teacherId=${user.id}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch course data');
      }

      if (!data.success || !data.course) {
        throw new Error('Course not found');
      }

      const apiCourse: ApiCourse = data.course;
      console.log('Fetched course data:', apiCourse); // Debug log

      // Extract module quizzes and final exam from API response
      const moduleQuizzes: ApiQuiz[] = apiCourse.moduleQuizzes || [];
      const finalExam: ApiQuiz | undefined = moduleQuizzes.find(quiz => quiz.quizType === 'final');
      const actualModuleQuizzes = moduleQuizzes.filter(quiz => quiz.quizType === 'module');

      console.log('Quiz breakdown:', {
        totalQuizzes: moduleQuizzes.length,
        moduleQuizzes: actualModuleQuizzes.length,
        finalExam: finalExam ? finalExam.id : 'none',
        quizzes: moduleQuizzes.map(q => ({ id: q.id, type: q.quizType, moduleId: q.moduleId, courseId: q.courseId }))
      });

      // Transform API data to wizard format
      const transformedData = transformApiCourseToWizardData(apiCourse, moduleQuizzes, finalExam);
      console.log('Transformed data:', transformedData); // Debug log
      setCourseData(transformedData);
      
    } catch (error) {
      console.error('Error fetching course:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch course data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCourseUpdate = async (updatedCourseData: CourseData) => {
    try {
      setIsUpdating(true);
      console.log('Starting course update with data:', updatedCourseData); // Debug log
      
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to update course');
        return;
      }

      // Transform wizard data back to API format
      const updateData = transformWizardDataToApiUpdate(updatedCourseData, id);
      console.log('Update data prepared:', updateData); // Debug log
      
      // Update course basic info
      const courseResponse = await fetch(`/api/courses/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: updateData.name,
          description: updateData.description,
          courseCode: updateData.courseCode,
          type: updateData.type,
          startDate: updateData.startDate,
          endDate: updateData.endDate,
          teacherId: user.id,
          // Add related course data
          admissions: updateData.admissions,
          academics: updateData.academics,
          tuitionAndFinancing: updateData.tuitionAndFinancing,
          careers: updateData.careers,
          studentExperience: updateData.studentExperience
        })
      });

      if (!courseResponse.ok) {
        const errorData = await courseResponse.json();
        throw new Error(errorData.error || 'Failed to update course');
      }

      // Process modules, chapters, and quizzes
      for (const moduleData of updateData.modules) {
        console.log(`Processing module: ${moduleData.name}`, {
          id: moduleData.id,
          hasModuleQuiz: !!moduleData.quiz,
          moduleQuizId: moduleData.quiz?.id
        });

        let currentModuleId = moduleData.id;

        if (moduleData.id) {
          // Update existing module
          const moduleResponse = await fetch(`/api/modules/${moduleData.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: moduleData.name,
              description: moduleData.description,
              orderIndex: moduleData.orderIndex,
              teacherId: user.id
            })
          });

          if (!moduleResponse.ok) {
            console.error('Failed to update module:', moduleData.id);
          }
        } else {
          // Create new module
          const newModuleResponse = await fetch('/api/modules', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: moduleData.name,
              description: moduleData.description,
              orderIndex: moduleData.orderIndex,
              courseId: parseInt(id),
              teacherId: user.id
            })
          });
          
          if (newModuleResponse.ok) {
            const newModule = await newModuleResponse.json();
            currentModuleId = newModule.module.id;
            console.log('Created new module with ID:', currentModuleId);
          }
        }

        // Update chapters
        for (const chapter of moduleData.chapters) {
          let currentChapterId = chapter.id;

          if (chapter.id) {
            // Update existing chapter
            const chapterResponse = await fetch(`/api/chapters/${chapter.id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: chapter.name,
                content: chapter.content,
                orderIndex: chapter.orderIndex,
                teacherId: user.id
              })
            });

            if (!chapterResponse.ok) {
              console.error('Failed to update chapter:', chapter.id);
            }
          } else {
            // Create new chapter
            const newChapterResponse = await fetch('/api/chapters', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: chapter.name,
                content: chapter.content,
                orderIndex: chapter.orderIndex,
                moduleId: currentModuleId,
                teacherId: user.id
              })
            });
            
            if (newChapterResponse.ok) {
              const newChapter = await newChapterResponse.json();
              currentChapterId = newChapter.chapter.id;
              console.log('Created new chapter with ID:', currentChapterId);
            }
          }

          // Handle chapter quiz
          if (chapter.quiz) {
            console.log(`Processing chapter quiz for chapter ${currentChapterId}:`, chapter.quiz.id);

            if (chapter.quiz.id) {
              // Update existing chapter quiz
              const quizResponse = await fetch(`/api/quizzes/${chapter.quiz.id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  name: chapter.quiz.name,
                  description: chapter.quiz.description,
                  quizType: 'chapter',
                  timeLimit: chapter.quiz.timeLimit,
                  minimumScore: chapter.quiz.minimumScore,
                  teacherId: user.id,
                  questions: chapter.quiz.questions
                })
              });

              if (!quizResponse.ok) {
                console.error('Failed to update chapter quiz:', chapter.quiz.id);
                const errorData = await quizResponse.json();
                console.error('Chapter quiz update error:', errorData);
              }
            } else {
              // Create new chapter quiz
              const quizResponse = await fetch('/api/quizzes', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  name: chapter.quiz.name,
                  description: chapter.quiz.description,
                  quizType: 'chapter',
                  timeLimit: chapter.quiz.timeLimit,
                  minimumScore: chapter.quiz.minimumScore,
                  chapterId: currentChapterId,
                  teacherId: user.id,
                  questions: chapter.quiz.questions
                })
              });

              if (!quizResponse.ok) {
                console.error('Failed to create chapter quiz');
                const errorData = await quizResponse.json();
                console.error('Chapter quiz creation error:', errorData);
              }
            }
          }
        }

        // Handle module quiz - FIXED: Proper handling
        if (moduleData.quiz) {
          console.log(`Processing module quiz for module ${currentModuleId}:`, {
            quizId: moduleData.quiz.id,
            quizName: moduleData.quiz.name,
            questionsCount: moduleData.quiz.questions.length
          });

          if (moduleData.quiz.id) {
            // Update existing module quiz
            const quizResponse = await fetch(`/api/quizzes/${moduleData.quiz.id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: moduleData.quiz.name,
                description: moduleData.quiz.description,
                quizType: 'module',
                timeLimit: moduleData.quiz.timeLimit,
                minimumScore: moduleData.quiz.minimumScore,
                moduleId: currentModuleId, // Ensure moduleId is set
                teacherId: user.id,
                questions: moduleData.quiz.questions
              })
            });

            if (!quizResponse.ok) {
              console.error('Failed to update module quiz:', moduleData.quiz.id);
              const errorData = await quizResponse.json();
              console.error('Module quiz update error:', errorData);
            } else {
              console.log('Successfully updated module quiz:', moduleData.quiz.id);
            }
          } else {
            // Create new module quiz
            const quizResponse = await fetch('/api/quizzes', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: moduleData.quiz.name,
                description: moduleData.quiz.description,
                quizType: 'module',
                timeLimit: moduleData.quiz.timeLimit,
                minimumScore: moduleData.quiz.minimumScore,
                moduleId: currentModuleId,
                teacherId: user.id,
                questions: moduleData.quiz.questions
              })
            });

            if (!quizResponse.ok) {
              console.error('Failed to create module quiz');
              const errorData = await quizResponse.json();
              console.error('Module quiz creation error:', errorData);
            } else {
              const newQuiz = await quizResponse.json();
              console.log('Successfully created module quiz:', newQuiz.quiz?.id);
            }
          }
        }
      }

      // Handle final exam - FIXED: Proper handling
      if (updateData.finalExam) {
        console.log('Processing final exam:', {
          examId: updateData.finalExam.id,
          examName: updateData.finalExam.name,
          questionsCount: updateData.finalExam.questions.length
        });

        if (updateData.finalExam.id) {
          // Update existing final exam
          const examResponse = await fetch(`/api/quizzes/${updateData.finalExam.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: updateData.finalExam.name,
              description: updateData.finalExam.description,
              quizType: 'final',
              timeLimit: updateData.finalExam.timeLimit,
              minimumScore: updateData.finalExam.minimumScore,
              courseId: parseInt(id), // Ensure courseId is set
              teacherId: user.id,
              questions: updateData.finalExam.questions
            })
          });

          if (!examResponse.ok) {
            console.error('Failed to update final exam:', updateData.finalExam.id);
            const errorData = await examResponse.json();
            console.error('Final exam update error:', errorData);
          } else {
            console.log('Successfully updated final exam:', updateData.finalExam.id);
          }
        } else {
          // Create new final exam
          const examResponse = await fetch('/api/quizzes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              name: updateData.finalExam.name,
              description: updateData.finalExam.description,
              quizType: 'final',
              timeLimit: updateData.finalExam.timeLimit,
              minimumScore: updateData.finalExam.minimumScore,
              courseId: parseInt(id),
              teacherId: user.id,
              questions: updateData.finalExam.questions
            })
          });

          if (!examResponse.ok) {
            console.error('Failed to create final exam');
            const errorData = await examResponse.json();
            console.error('Final exam creation error:', errorData);
          } else {
            const newExam = await examResponse.json();
            console.log('Successfully created final exam:', newExam.quiz?.id);
          }
        }
      }

      toast.success('Course updated successfully!');
      router.push('/dashboard/teacher/courses');
      
    } catch (error) {
      console.error('Error updating course:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update course');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/teacher/courses');
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <Link href="/dashboard/teacher/courses">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">Edit Course</h1>
          <p className="text-muted-foreground">Update course information and content</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-5 w-5" />
              Error Loading Course
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchCourseData} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!courseData) {
    return (
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <Link href="/dashboard/teacher/courses">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </Button>
          </Link>
          <h1 className="text-3xl font-bold">Edit Course</h1>
          <p className="text-muted-foreground">Update course information and content</p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Course Not Found</CardTitle>
            <CardDescription>The requested course could not be found.</CardDescription>
          </CardHeader>
          <CardContent>
            <Link href="/dashboard/teacher/courses">
              <Button>Back to Courses</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <Link href="/dashboard/teacher/courses">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Courses
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">Edit Course</h1>
        <p className="text-muted-foreground">Update course information and content</p>
      </div>

      <CourseCreationWizard
        initialData={courseData}
        onComplete={handleCourseUpdate}
        onCancel={handleCancel}
      />
    </div>
  );
}